import { useState } from "react";
import { WidgetsProvider, WidgetsCatalog, Layout } from "./lib";
import type { Widget, PlacedWidget } from "./lib";
import type { Layout as RGLLayout } from "react-grid-layout";
import "./App.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { CounterWidget } from "./example-widgets/CounterWidget";
import { GreetingWidget } from "./example-widgets/GreetingWidget";
import { TodoWidget } from "./example-widgets/TodoWidget";

// Define example widgets
const exampleWidgets: Widget[] = [
  {
    id: "counter",
    name: "Counter Widget",
    description: "A simple counter that can increment and decrement values",
    component: CounterWidget,
    category: "Interactive",
    tags: ["counter", "interactive", "demo"],
    props: { initialValue: 0, step: 1 },
    layout: { w: 3, h: 4, x: 0, y: 0 },
  },
  {
    id: "greeting",
    name: "Greeting Widget",
    description: "A personalized greeting widget with name input",
    component: GreetingWidget,
    category: "Interactive",
    tags: ["greeting", "input", "demo"],
    props: { defaultName: "Developer" },
    layout: { w: 4, h: 3, x: 3, y: 0 },
  },
  {
    id: "todo",
    name: "Todo List Widget",
    description: "A simple todo list to manage tasks",
    component: TodoWidget,
    category: "Productivity",
    tags: ["todo", "list", "productivity", "tasks"],
    layout: { w: 5, h: 6, x: 7, y: 0 },
  },
];

function App() {
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);
  const [placedWidgets, setPlacedWidgets] = useState<PlacedWidget[]>([]);



  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    console.log("Drop event:", { layout, item, e });

    // Get the drag data from the event
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData("application/json");

    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        console.log(data, "dropped widget data");

        if (data.type === 'widget' && data.widget) {
          // Generate unique ID for this widget instance
          const instanceId = `${data.widget.id}-${Date.now()}`;

          // Create layout item for the dropped widget
          const newLayoutItem: RGLLayout = {
            i: instanceId,
            x: item.x,
            y: item.y,
            w: data.widget.layout?.w || 4,
            h: data.widget.layout?.h || 4,
            minW: data.widget.layout?.minW,
            minH: data.widget.layout?.minH,
            maxW: data.widget.layout?.maxW,
            maxH: data.widget.layout?.maxH,
            static: data.widget.layout?.static || false,
            isDraggable: data.widget.layout?.isDraggable !== false,
            isResizable: data.widget.layout?.isResizable !== false,
          };

          // Create placed widget
          const newPlacedWidget: PlacedWidget = {
            id: instanceId,
            widget: data.widget,
            layout: newLayoutItem,
          };

          // Update states
          setPlacedWidgets(prev => [...prev, newPlacedWidget]);
          setCurrentLayout(prev => [...prev, newLayoutItem]);
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    console.log(layout, "layout ");
    setCurrentLayout(layout);

    // Update placed widgets with new layout positions
    setPlacedWidgets(prev =>
      prev.map(placedWidget => {
        const updatedLayoutItem = layout.find(item => item.i === placedWidget.id);
        if (updatedLayoutItem) {
          return {
            ...placedWidget,
            layout: updatedLayoutItem,
          };
        }
        return placedWidget;
      })
    );
  };

  const handleDrag = (widget: Widget) => {
    console.log("Dragging widget:", widget.name);
  };

  const removeWidget = (widgetId: string) => {
    setPlacedWidgets(prev => prev.filter(w => w.id !== widgetId));
    setCurrentLayout(prev => prev.filter(item => item.i !== widgetId));
  };

  return (
    <WidgetsProvider initialWidgets={exampleWidgets}>
      <div
        style={{
          minHeight: "100vh",
          width: "100%",
        }}
      >
        <div style={{ display: "flex", gap: "24px", minHeight: "1000px" }}>
          {/* Widget Catalog */}
          <div
            style={{
              width: "350px",
              flexShrink: 0,
              backgroundColor: "#fff",
              border: "1px solid #e0e0e0",
              borderRadius: "12px",
              padding: "20px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              height: "fit-content",
            }}
          >
            <div style={{ marginBottom: "16px" }}>
              <h3
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "20px",
                  fontWeight: "600",
                  color: "#2c3e50",
                }}
              >
                🧩 Widget Catalog
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#7f8c8d",
                  margin: 0,
                  lineHeight: "1.4",
                }}
              >
                Drag widgets to the layout area to add them →
              </p>
            </div>
            <WidgetsCatalog
              widgets={exampleWidgets}
              onDrag={handleDrag}
              enableDrag={true}
              showSearch={false}
              showCategories={true}
            />
          </div>

          {/* Layout Area */}
          <div
            style={{
              flex: 1,
              border: "2px dashed #3498db",
              borderRadius: "16px",
              minHeight: "900px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#fff",
              boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                padding: "20px",
                borderBottom: "1px solid #ecf0f1",
                backgroundColor:
                  "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                color: "white",
              }}
            >
            </div>
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                minHeight: "800px",
                padding: "20px",
                backgroundColor: "#fafbfc",
                overflow: "hidden",
              }}
            >
              <div className="layout-container" style={{ position: "relative" }}>
                <Layout
                  onDrop={handleDrop}
                  onLayoutChange={handleLayoutChange}
                  layout={currentLayout}
                  cols={12}
                  rowHeight={50} // Even larger row height for better visibility
                  width={1400} // Increased width for more space
                  margin={[16, 16]} // Larger margins
                  containerPadding={[24, 24]} // More padding
                  isDraggable={true}
                  isResizable={true}
                  isDroppable={true}
                  droppingItem={{
                    i: "__dropping-elem__",
                    h: 8,
                    w: 8,
                  }}
                >
                  {placedWidgets.map((placedWidget) => {
                    const WidgetComponent = placedWidget.widget.component;

                    // Debug: Check if component is undefined
                    if (!WidgetComponent) {
                      console.error('Widget component is undefined for widget:', placedWidget.widget);
                      return null;
                    }

                    return (
                      <div
                        key={placedWidget.id}
                        style={{
                          width: "100%",
                          height: "100%",
                          overflow: "hidden",
                          backgroundColor: "#fff",
                          borderRadius: "8px",
                          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                          position: "relative",
                        }}
                      >
                        {/* Remove button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeWidget(placedWidget.id);
                          }}
                          style={{
                            position: "absolute",
                            top: "4px",
                            right: "4px",
                            width: "24px",
                            height: "24px",
                            borderRadius: "50%",
                            border: "none",
                            backgroundColor: "#dc3545",
                            color: "white",
                            cursor: "pointer",
                            fontSize: "12px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            zIndex: 1000,
                            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
                          }}
                          title="Remove widget"
                        >
                          ×
                        </button>
                        <WidgetComponent {...(placedWidget.widget.props || {})} />
                      </div>
                    );
                  })}
                </Layout>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WidgetsProvider>
  );
}

export default App;
