import React from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { LayoutProps } from './types';

const ResponsiveGridLayout = WidthProvider(Responsive);

export const Layout: React.FC<LayoutProps> = ({
  // Only grid layout props
  children,
  className = '',
  onDrop,
  onLayoutChange,
  layout = [],
  cols = 12,
  rowHeight = 30,
  margin = [10, 10],
  containerPadding = [10, 10],
  isDraggable = true,
  isResizable = true,
  isDroppable = true,
  droppingItem = { i: '__dropping-elem__', w: 4, h: 5 },
  breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  colsByBreakpoint = { lg: cols, md: 10, sm: 6, xs: 4, xxs: 2 },
  layoutClassName = '',
  layoutStyle = {},
}) => {
  // Always render grid layout
  return (
    <div className={`layout-grid ${className} ${layoutClassName}`} style={layoutStyle}>
      <ResponsiveGridLayout
        className="react-grid-layout"
        layouts={{ lg: layout }}
        breakpoints={breakpoints}
        cols={colsByBreakpoint}
        rowHeight={rowHeight}
        margin={margin}
        containerPadding={containerPadding}
        isDraggable={isDraggable}
        isResizable={isResizable}
        isDroppable={isDroppable}
        droppingItem={{ i: droppingItem.i || '__dropping-elem__', w: droppingItem.w || 4, h: droppingItem.h || 5 }}
        onDrop={onDrop}
        onLayoutChange={onLayoutChange}
        width={1400}
      >
        {children}
      </ResponsiveGridLayout>
    </div>
  );
};

export default Layout;
